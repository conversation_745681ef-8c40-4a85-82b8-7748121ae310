# YouTube Clipper Enhanced - Chrome插件 + yt-dlp 智能视频片段处理系统

## 🎯 项目概述

YouTube Clipper Enhanced 是一个功能强大的Chrome插件，专为YouTube视频片段的精确收集、智能命名和批量处理而设计。插件通过直观的用户界面收集片段信息，自动生成专业的yt-dlp命令，实现视频片段的批量下载、重命名和后处理。

## ✨ 核心功能特性

### 🎬 智能片段收集
- **自动时间戳输入**：一键获取YouTube当前播放时间
- **精确时间控制**：支持微调功能（±1s, ±5s, ±10s）
- **多片段管理**：支持单个视频的多个片段设置
- **片段自定义命名**：为每个片段设置个性化名称

### 📝 智能文件命名系统
**自动生成格式：** `[片段名称]_[时长]s_[下载日期]_[视频ID]_[上传日期]_[上传者]_[播放量].mp4`

**示例文件名：** `精彩开场_45s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4`

### 🛠️ yt-dlp集成处理
- **命令自动生成**：基于收集数据生成完整yt-dlp命令
- **批量处理支持**：一次性处理多个片段
- **高级后处理**：支持FFmpeg滤镜（高斯模糊、裁剪等）
- **元数据嵌入**：自动嵌入视频元信息

### 🎨 高级视频处理
- **区域高斯模糊**：指定区域和时间范围的模糊效果
- **视频预览窗口**：实时预览模糊效果，支持区域选择和参数调整
- **智能裁剪**：自定义视频裁剪参数
- **滤镜组合**：支持多种FFmpeg滤镜组合应用

## 🏗️ 技术架构

### 前端技术栈
- **框架**：Svelte + TypeScript
- **构建工具**：Vite + @crxjs/vite-plugin
- **样式**：Tailwind CSS
- **状态管理**：Svelte Stores
- **云同步**：Firebase Firestore

### Chrome插件架构
- **Popup界面**：主要用户交互界面
- **Content Script**：YouTube页面注入脚本
- **Background Script**：后台服务处理
- **Options页面**：高级设置配置

### 数据结构设计
```typescript
interface IVideoClip {
  start: number;           // 开始时间（秒）
  end: number;            // 结束时间（秒）
  name: string;           // 片段自定义名称
}

interface IVideo {
  id: string;             // YouTube视频ID
  title: string;          // 视频标题
  uploader: string;       // 上传者
  uploadDate: string;     // 上传日期
  viewCount: number;      // 播放量
  duration: number;       // 视频总时长
  clips: IVideoClip[];    // 片段列表
  loop: boolean;          // 是否循环播放
}
```

## 🎮 用户界面设计

### 主界面功能
1. **视频信息显示**：当前YouTube视频的基本信息
2. **片段管理区域**：
   - 片段名称输入框
   - 开始/结束时间输入框
   - "获取当前时间"按钮
   - 时间微调按钮（±1s, ±5s, ±10s）
   - 添加/删除片段按钮

3. **命令生成区域**：
   - yt-dlp命令实时预览
   - 一键复制命令按钮
   - 高级选项设置

4. **视频预览窗口**：
   - 实时视频预览画面
   - 可视化区域选择工具
   - 模糊效果实时预览
   - 区域坐标显示和微调

### 设置页面功能
1. **文件命名模板配置**
2. **高斯模糊参数设置**
3. **视频预览配置**
4. **输出格式选择**
5. **预设模板管理**

## 🔧 yt-dlp命令生成系统

### 基础命令结构
```bash
yt-dlp "https://www.youtube.com/watch?v=VIDEO_ID" \
  --download-sections "*开始时间-结束时间" \
  --output "文件命名模板" \
  --embed-metadata \
  --parse-metadata "title:%(uploader)s_%(title)s"
```

### 智能文件命名模板
```bash
--output "[片段名称]_%(section_duration)ss_%(download_date)s_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s.%(ext)s"
```

### 高级处理选项
```bash
# 区域高斯模糊
--postprocessor-args "ffmpeg:-vf crop=W:H:X:Y,blur=sigma=强度:enable='between(t,开始,结束)'"

# 多片段批量处理
--download-sections "*00:10:15-00:20:30" \
--download-sections "*00:45:00-00:50:15" \
--download-sections "*01:20:00-01:25:30"
```

## 📋 实施计划

### 阶段1：核心功能增强 (优先级：高)
- [ ] 扩展片段数据结构，添加name字段
- [ ] 实现自动时间戳获取功能
- [ ] 添加片段命名输入界面
- [ ] 实现时间微调按钮功能

### 阶段2：元数据收集 (优先级：高)
- [ ] 集成YouTube Data API获取视频元数据
- [ ] 实现播放量、上传日期等信息收集
- [ ] 扩展视频数据结构存储元信息

### 阶段3：命令生成器 (优先级：中)
- [ ] 开发yt-dlp命令生成逻辑
- [ ] 实现智能文件命名模板
- [ ] 添加命令预览和复制功能
- [ ] 支持批量命令生成

### 阶段4：高级处理功能 (优先级：中)
- [ ] 高斯模糊参数设置界面
- [ ] 视频预览窗口开发
- [ ] 可视化区域选择工具
- [ ] 实时模糊效果预览
- [ ] FFmpeg滤镜组合配置
- [ ] 预设模板管理系统
- [ ] 命令历史记录功能

### 阶段5：用户体验优化 (优先级：低)
- [ ] 快捷键支持
- [ ] 拖拽排序片段
- [ ] 批量导入/导出功能
- [ ] 多语言支持

## 🔍 技术实现细节

### 自动时间戳获取实现
```javascript
// Content Script中获取当前播放时间
function getCurrentTime() {
  const video = document.querySelector('video');
  return video ? video.currentTime : null;
}

// 通过Chrome消息传递API发送到Popup
chrome.runtime.sendMessage({
  type: 'GET_CURRENT_TIME',
  time: getCurrentTime()
});
```

### YouTube元数据获取
```javascript
// 使用YouTube Data API获取视频详细信息
async function getVideoMetadata(videoId) {
  const response = await fetch(
    `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=snippet,statistics&key=${API_KEY}`
  );
  const data = await response.json();
  return {
    uploader: data.items[0].snippet.channelTitle,
    uploadDate: data.items[0].snippet.publishedAt,
    viewCount: data.items[0].statistics.viewCount
  };
}
```

### 命令生成逻辑
```javascript
function generateYtDlpCommand(video, clips, options) {
  let command = `yt-dlp "${video.url}"`;

  // 添加片段参数
  clips.forEach(clip => {
    const start = secondsToTimeString(clip.start);
    const end = secondsToTimeString(clip.end);
    command += ` --download-sections "*${start}-${end}"`;
  });

  // 添加文件命名模板
  const template = generateNamingTemplate(options);
  command += ` --output "${template}"`;

  // 添加元数据嵌入
  command += ` --embed-metadata`;

  return command;
}
```

### 智能文件命名模板生成
```javascript
function generateNamingTemplate(clip, video, options) {
  const downloadDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const duration = clip.end - clip.start;

  // 基础模板：[片段名称]_[时长]s_[下载日期]_[视频ID]_[上传日期]_[上传者]_[播放量]
  return `${clip.name}_${duration}s_${downloadDate}_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s.%(ext)s`;
}
```

### 视频预览窗口实现
```javascript
// 视频预览组件
class VideoPreviewWindow {
  constructor(videoElement, canvas) {
    this.video = videoElement;
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.blurRegions = [];
    this.isSelecting = false;
  }

  // 绘制预览帧
  drawPreview() {
    this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
    this.drawBlurRegions();
  }

  // 添加模糊区域
  addBlurRegion(x, y, width, height, intensity) {
    this.blurRegions.push({ x, y, width, height, intensity });
    this.updateFFmpegFilter();
  }

  // 生成FFmpeg滤镜参数
  updateFFmpegFilter() {
    const filters = this.blurRegions.map(region =>
      `crop=${region.width}:${region.height}:${region.x}:${region.y},blur=sigma=${region.intensity}`
    );
    return filters.join(',');
  }
}
```

## 🎯 预期效果

### 用户工作流程
1. **浏览YouTube视频**，找到需要剪辑的片段
2. **打开插件**，自动识别当前视频
3. **设置片段**：
   - 播放到开始位置，点击"获取当前时间"设置开始点
   - 播放到结束位置，点击"获取当前时间"设置结束点
   - 输入片段自定义名称
4. **重复步骤3**添加多个片段
5. **生成命令**：插件自动生成完整的yt-dlp命令
6. **复制执行**：一键复制命令到终端执行

### 输出文件示例
```
精彩开场_45s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
搞笑片段_30s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
结尾彩蛋_60s_20241218_dQw4w9WgXcQ_20091025_RickAstleyVEVO_1.2B.mp4
```

### 完整命令示例
```bash
# 单个片段处理
yt-dlp "https://www.youtube.com/watch?v=dQw4w9WgXcQ" \
  --download-sections "*00:01:30-00:02:15" \
  --output "精彩开场_45s_20241218_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s.%(ext)s" \
  --embed-metadata

# 多片段批量处理
yt-dlp "https://www.youtube.com/watch?v=dQw4w9WgXcQ" \
  --download-sections "*00:01:30-00:02:15" \
  --download-sections "*00:05:00-00:05:30" \
  --download-sections "*00:08:45-00:09:45" \
  --output "%(section_title)s_%(section_duration)ss_20241218_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s.%(ext)s" \
  --embed-metadata

# 带高斯模糊的高级处理
yt-dlp "https://www.youtube.com/watch?v=dQw4w9WgXcQ" \
  --download-sections "*00:01:30-00:02:15" \
  --postprocessor-args "ffmpeg:-vf blur=sigma=5:enable='between(t,10,20)'" \
  --output "模糊片段_45s_20241218_%(id)s_%(upload_date)s_%(uploader)s_%(view_count)s.%(ext)s" \
  --embed-metadata
```

## 🚀 项目优势

1. **无需复杂通信**：避免Chrome插件与本地服务的复杂通信机制
2. **批量自动化**：一条命令处理多个片段和多种效果
3. **高度可定制**：支持复杂的FFmpeg滤镜组合和文件命名
4. **用户友好**：直观的界面设计，简化复杂的命令行操作
5. **数据持久化**：支持云同步，跨设备使用
6. **扩展性强**：模块化设计，易于添加新功能

## 📊 项目完成度评估

### ✅ 已完成功能 (80%)
- **基础Chrome插件架构**：完整的Svelte + TypeScript + Vite构建系统
- **片段时间管理**：开始/结束时间设置，多片段支持
- **数据存储系统**：本地存储 + Firebase云同步
- **用户界面**：现代化UI设计，深色/浅色主题
- **基础播放控制**：循环播放，片段跳转功能

### 🔄 待增强功能 (30%)
- **✅ 片段命名系统**：自定义片段名称输入
- **✅ 自动时间戳获取**：一键获取当前播放时间
- **✅ 视频片段精确跳转系统
  - 支持从剪藏列表直接跳转到视频特定时间点
  - 实现带时间戳的精确跳转功能
  - 优化片段预览和跳转用户界面
- **YouTube元数据收集**：播放量、上传日期等信息
- **yt-dlp命令生成器**：智能命令生成和复制
- **视频预览窗口**：实时预览和区域选择工具
- **高级处理选项**：高斯模糊、滤镜配置

## 🎨 高级功能规划

### FFmpeg滤镜支持
```bash
# 区域模糊 + 裁剪组合
--postprocessor-args "ffmpeg:-vf crop=1280:720:0:0,blur=sigma=3:enable='between(t,2,8)'"

# 多区域不同强度模糊
--postprocessor-args "ffmpeg:-vf blur=sigma=3:enable='between(t,10,20)',blur=sigma=8:enable='between(t,30,40)'"

# 透明度 + 模糊组合
--postprocessor-args "ffmpeg:-vf blur=sigma=5:enable='between(t,5,10)',colorkey=0x000000:0.3:0.1"
```

### 预设模板系统
```javascript
const presetTemplates = {
  "教程片段": {
    naming: "教程_%(section_title)s_%(duration)ss_%(date)s",
    filters: "crop=1920:1080:0:0",
    quality: "best[height<=1080]"
  },
  "精彩集锦": {
    naming: "精彩_%(section_title)s_%(duration)ss_%(uploader)s",
    filters: "blur=sigma=2:enable='between(t,0,2)'",
    quality: "best[height<=720]"
  }
};
```

## 📚 相关资源

### 官方文档
- [yt-dlp官方文档](https://github.com/yt-dlp/yt-dlp) - 主要下载工具
- [FFmpeg滤镜文档](https://ffmpeg.org/ffmpeg-filters.html) - 视频处理滤镜
- [Chrome扩展开发指南](https://developer.chrome.com/docs/extensions/) - 插件开发
- [YouTube Data API](https://developers.google.com/youtube/v3) - 视频元数据获取

### 技术参考
- [Svelte官方文档](https://svelte.dev/docs) - 前端框架
- [Vite构建工具](https://vitejs.dev/) - 开发构建
- [Tailwind CSS](https://tailwindcss.com/) - 样式框架
- [Firebase文档](https://firebase.google.com/docs) - 云服务

## 🔧 开发环境设置

### 环境要求
- Node.js 16+
- Chrome浏览器
- yt-dlp命令行工具
- FFmpeg（用于视频处理）

### 安装步骤
```bash
# 1. 克隆项目
git clone https://github.com/ylfyt/youtube-clipper.git
cd youtube-clipper

# 2. 安装依赖
yarn install

# 3. 开发模式
yarn dev

# 4. 构建生产版本
yarn build
```

### Chrome插件加载
1. 打开Chrome扩展管理页面 `chrome://extensions/`
2. 启用"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择项目的`dist`目录

## 🎯 下一步行动计划

### 立即可实施 (1-2周)
1. **扩展数据结构**：为IVideoClip添加name字段
2. **实现时间戳获取**：添加"获取当前时间"按钮
3. **片段命名界面**：在现有UI中添加名称输入框
4. **基础命令生成**：实现简单的yt-dlp命令生成逻辑

### 中期目标 (2-4周)
1. **YouTube API集成**：获取视频元数据
2. **智能文件命名**：实现完整的命名模板系统
3. **命令预览界面**：添加命令生成和复制功能
4. **视频预览窗口**：开发实时预览和区域选择功能
5. **基础滤镜支持**：实现高斯模糊参数设置

### 长期规划 (1-2月)
1. **高级滤镜系统**：复杂FFmpeg滤镜组合
2. **预设模板管理**：保存和管理常用配置
3. **批量处理优化**：多视频批量命令生成
4. **用户体验提升**：快捷键、拖拽等交互优化

---

*本项目将YouTube视频片段处理从复杂的命令行操作转化为直观的图形界面操作，大大降低了用户的使用门槛，同时保持了yt-dlp强大的处理能力和灵活性。通过智能的文件命名和批量处理功能，用户可以高效地管理和处理大量视频片段。*