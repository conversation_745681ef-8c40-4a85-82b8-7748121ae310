{"name": "youtube-clipper", "description": "Youtube Clipper", "version": "1.0.0", "type": "module", "repository": {"type": "git", "url": "https://github.com/ylfyt/youtube-clipper.git"}, "scripts": {"dev": "vite", "build": "vite build", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@crxjs/vite-plugin": "2.0.0-beta.18", "@sveltejs/vite-plugin-svelte": "^2.5.0", "@tsconfig/svelte": "5.0.2", "@types/chrome": "0.0.243", "autoprefixer": "^10.4.16", "firebase": "^10.5.0", "postcss": "^8.4.30", "svelte": "^4.2.0", "svelte-check": "^3.8.0", "svelte-preprocess": "^5.1.0", "tailwindcss": "^3.4.0", "tslib": "2.6.2", "typescript": "^5.5.0", "vite": "^4.5.0"}}