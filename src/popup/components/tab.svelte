<script lang="ts">
	import ForwardIcon from "@/assets/svg/forward-icon.svelte";
	import LoopIcon from "@/assets/svg/loop-icon.svelte";
	import MinusIcon from "@/assets/svg/minus-icon.svelte";
	import MutedIcon from "@/assets/svg/muted-icon.svelte";
	import NextIcon from "@/assets/svg/next-icon.svelte";
	import PauseIcon from "@/assets/svg/pause-icon.svelte";
	import PlayIcon from "@/assets/svg/play-icon.svelte";
	import PlusIcon from "@/assets/svg/plus-icon.svelte";
	import PrevIcon from "@/assets/svg/prev-icon.svelte";
	import RewindIcon from "@/assets/svg/rewind-icon.svelte";
	import ShuffleIcon from "@/assets/svg/shuffle-icon.svelte";
	import VolumeIcon from "@/assets/svg/volume-icon.svelte";
	import type { ITab } from "@/interfaces/tab";
	import { YtEvents, dispatchYtEvent, seekVideoYt, setYtVolume, shuffleYt, toggleLoopYt, type SeekVideoYtArgs, type SetYtVolumeArgs, type ToggleLoopYtArgs } from "@/utils/yt-utils";
	import Button from "./button.svelte";
	import ArrowRight from "./icons/arrow-right.svelte";
	import VideoPlaylist from "./video-playlist.svelte";
	import { secondToTimeString } from "@/utils/second-to-time-string";

	export let tab: ITab;
	export let tabs: ITab[];
	export let idx: number;

	let showPlaylist = idx === 0 && tab.isYoutube && tab.isPlaylist;
	let youtubeVolume = tab.volume;
	let seekPosition = tab.duration === 0 ? 0 : (tab.currentTime / tab.duration) * 100;

	const toggleMute = async (tabId: number, state: boolean) => {
		await chrome.tabs.update(tabId, { muted: !state });
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isMuted = !state;
			}
			return tab;
		});
	};

	const refreshTabs = () => {
		setTimeout(async () => {
			const newTab = await chrome.tabs.get(tab.id);
			tabs = tabs.map((el) => {
				if (el.id === tab.id) el.title = newTab.title ?? "";
				return el;
			});
		}, 2000);
	};

	const setVolume = async () => {
		await chrome.scripting.executeScript<SetYtVolumeArgs, number>({
			func: setYtVolume,
			target: { tabId: tab.id },
			world: "MAIN",
			args: [youtubeVolume],
		});
	};

	const next = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.next()],
		});

		refreshTabs();
	};

	const prev = async (tabId: number) => {
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [YtEvents.prev()],
		});

		refreshTabs();
	};

	const togglePlay = async (tabId: number) => {
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isPaused = !tab.isPaused;
			}
			return tab;
		});
		await chrome.scripting.executeScript({
			func: dispatchYtEvent,
			target: { tabId },
			args: [tab.isYoutubeMusic ? YtEvents.playToggleMusic() : YtEvents.playToggle()],
		});
	};

	const toggleLoop = async (tabId: number) => {
		const res = await chrome.scripting.executeScript<ToggleLoopYtArgs, { loopState: number }>({
			func: toggleLoopYt,
			target: { tabId },
			world: "MAIN",
			args: [{ isPlaylist: tab.isPlaylist, isYoutubeMusic: !!tab.isYoutubeMusic }],
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.loopState = res[0].result.loopState;
			}
			return tab;
		});
	};

	const toggleShuffle = async (tabId: number) => {
		if (tab.isYoutubeMusic) {
			await chrome.scripting.executeScript({
				func: dispatchYtEvent,
				target: { tabId },
				args: [YtEvents.shuffleMusic()],
			});
			return;
		}

		await chrome.scripting.executeScript({
			func: shuffleYt,
			target: { tabId },
			world: "MAIN",
		});
		tabs = tabs.map((tab) => {
			if (tab.id === tabId) {
				tab.isShuffled = !tab.isShuffled;
			}
			return tab;
		});
	};

	const seekVideo = async (tabId: number, second: number, to?: number) => {
		await chrome.scripting.executeScript<SeekVideoYtArgs, void>({
			func: seekVideoYt,
			target: { tabId },
			world: "MAIN",
			args: [{ second, to }],
		});
	};

	const openTab = async () => {
		await chrome.tabs.update(tab.id, {
			active: true,
		});
	};
</script>

<div class="flex w-full justify-between border border-gray-200 dark:border-gray-600 p-4 gap-3 rounded-xl flex-col
			 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700
			 shadow-lg hover:shadow-xl transition-all duration-200 relative">
	<button on:click={openTab} title="Open Tab"
			class="absolute top-3 right-3 fill-gray-600 dark:fill-orange-400 hover:fill-orange-500 dark:hover:fill-orange-300
				   p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200">
		<ArrowRight width={18} />
	</button>
	<div class="flex items-start gap-2 min-w-0 pr-10">
		{#if tab.iconUrl}
			<img width="18px" src={tab.iconUrl} alt="icon" />
		{/if}
		<span class="text-sm font-medium text-gray-800 dark:text-gray-200 break-words leading-relaxed" title={tab.title}>
			{tab.title}
		</span>
	</div>
	{#if tab.duration}
		<div class="flex items-center gap-2">
			<button
				title="Rewind 10s"
				class="bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
					   rounded-lg w-8 h-8 flex justify-center items-center fill-white
					   shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
					   hover:-translate-y-0.5 active:transform-none"
				on:click={() => seekVideo(tab.id, -10)}
			>
				<RewindIcon width={12} />
			</button>
			<span class="text-sm text-gray-700 dark:text-gray-300">{secondToTimeString(Math.floor((seekPosition / 100) * tab.duration))}</span>
			<input bind:value={seekPosition} type="range" min="0" max={100} class="flex-1" on:change={() => seekVideo(tab.id, 0, (seekPosition / 100) * tab.duration)} />
			<span class="text-sm text-gray-700 dark:text-gray-300">{secondToTimeString(Math.floor(tab.duration))}</span>
			<button
				title="Forward 10s"
				class="bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
					   rounded-lg w-8 h-8 flex justify-center items-center fill-white
					   shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
					   hover:-translate-y-0.5 active:transform-none"
				on:click={() => seekVideo(tab.id, 10)}
			>
				<ForwardIcon width={12} />
			</button>
		</div>
	{/if}
	<div class="flex gap-4">
		<button
			title="Mute/Unmute"
			on:click={() => toggleMute(tab.id, tab.isMuted)}
			class="bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700
				   rounded-lg w-8 h-8 flex justify-center items-center fill-white
				   shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
				   hover:-translate-y-0.5 active:transform-none"
		>
			{#if tab.isMuted}
				<MutedIcon width={12} />
			{:else}
				<VolumeIcon width={12} />
			{/if}
		</button>
		{#if tab.isYoutube}
			<div class="flex gap-2 items-center">
				<input bind:value={youtubeVolume} type="range" min="0" max="100" class="w-20" on:change={setVolume} />
				<span class="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-800 dark:to-blue-900
							 text-blue-800 dark:text-blue-200 w-[35px] rounded-lg flex items-center justify-center h-full
							 shadow-sm border border-blue-200 dark:border-blue-700 font-semibold text-xs">{Math.floor(youtubeVolume)}</span>
			</div>
			<div class="flex items-center justify-center gap-2">
				<button
					title="Prev"
					class={`bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
							rounded-lg w-8 h-8 flex justify-center items-center fill-white
							shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
							hover:-translate-y-0.5 active:transform-none
							${!tab.isPlaylist ? "invisible" : "visible"}`}
					on:click={() => prev(tab.id)}
				>
					<PrevIcon width={12} />
				</button>
				<button
					title="Play/Pause"
					class="bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
						   rounded-lg w-8 h-8 flex justify-center items-center fill-white
						   shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
						   hover:-translate-y-0.5 active:transform-none"
					on:click={() => togglePlay(tab.id)}
				>
					{#if tab.isPaused}
						<PlayIcon width={12} />
					{:else}
						<PauseIcon width={12} />
					{/if}
				</button>
				<button
					title="Next"
					class="bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700
						   rounded-lg w-8 h-8 flex justify-center items-center fill-white
						   shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
						   hover:-translate-y-0.5 active:transform-none"
					on:click={() => next(tab.id)}
				>
					<NextIcon width={12} />
				</button>
			</div>
			<div class="flex gap-2">
				<button
					title={`Loop ${tab.loopState ? "ON" : "OFF"}`}
					class={`rounded-lg w-8 h-8 flex justify-center items-center fill-white
							shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
							hover:-translate-y-0.5 active:transform-none
							${!tab.isPlaylist || tab.isPlaylistMix
								? (!tab.loopState
									? "bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
									: "bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700")
								: (!tab.loopState
									? "bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
									: tab.loopState === 1
										? "bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
										: "bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700")}`}
					on:click={() => toggleLoop(tab.id)}
				>
					<LoopIcon width={12} />
				</button>
				{#if tab.isPlaylist && !tab.isPlaylistMix}
					<button
						title={`Shuffle ${tab.isShuffled ? "ON" : "OFF"}`}
						class={`rounded-lg w-8 h-8 flex justify-center items-center fill-white
								shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
								hover:-translate-y-0.5 active:transform-none
								${tab.isShuffled
									? "bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
									: "bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"}`}
						on:click={() => toggleShuffle(tab.id)}
					>
						<ShuffleIcon width={12} />
					</button>
				{/if}
			</div>
			{#if tab.isPlaylist}
				<div class="ml-auto">
					<button
						class={`bg-gradient-to-br from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700
								rounded-lg w-8 h-8 flex justify-center items-center text-white text-base
								shadow-md hover:shadow-lg active:shadow-inner transition-all duration-200
								hover:-translate-y-0.5 active:transform-none
								${showPlaylist ? "" : "-rotate-90"}`}
						on:click={() => (showPlaylist = !showPlaylist)}
					>
						🔽
					</button>
				</div>
			{/if}
		{/if}
	</div>
	{#if showPlaylist}
		<VideoPlaylist {refreshTabs} tabId={tab.id} />
	{/if}
</div>