/**
 * 浏览器检测工具
 * 用于自动检测当前浏览器类型，并判断是否为 yt-dlp 支持的 cookie 浏览器
 */

/**
 * yt-dlp 支持的浏览器列表
 * 参考：yt-dlp下载指令参数.md
 */
export const SUPPORTED_BROWSERS = [
    'brave',
    'chrome',
    'chromium',
    'edge', 
    'firefox',
    'opera',
    'safari',
    'vivaldi',
    'whale'
] as const;

export type SupportedBrowser = typeof SUPPORTED_BROWSERS[number];

/**
 * 浏览器检测结果接口
 */
export interface BrowserDetectionResult {
    /** 检测到的浏览器名称 */
    browser: string;
    /** 是否为 yt-dlp 支持的浏览器 */
    isSupported: boolean;
    /** 如果支持，返回对应的 yt-dlp 浏览器参数名 */
    ytDlpBrowserName?: SupportedBrowser;
    /** 浏览器版本（如果可获取） */
    version?: string;
    /** 用户代理字符串 */
    userAgent: string;
}

/**
 * 检测当前浏览器类型
 * @returns 浏览器检测结果
 */
export function detectBrowser(): BrowserDetectionResult {
    const userAgent = navigator.userAgent;
    const result: BrowserDetectionResult = {
        browser: 'unknown',
        isSupported: false,
        userAgent
    };

    // 检测各种浏览器
    // 注意：检测顺序很重要，因为某些浏览器的 UA 包含其他浏览器的标识

    // 检测 ARC 浏览器 (基于 Chromium，但有特殊标识)
    if (userAgent.includes('Arc/')) {
        result.browser = 'arc';
        result.isSupported = false; // ARC 不在 yt-dlp 官方支持列表中，但可以使用 chrome 参数
        result.ytDlpBrowserName = 'chrome'; // 可以尝试使用 chrome 参数
    } else if (userAgent.includes('Whale')) {
        result.browser = 'whale';
        result.isSupported = true;
        result.ytDlpBrowserName = 'whale';
    } else if (userAgent.includes('Vivaldi')) {
        result.browser = 'vivaldi';
        result.isSupported = true;
        result.ytDlpBrowserName = 'vivaldi';
    } else if (userAgent.includes('Brave')) {
        result.browser = 'brave';
        result.isSupported = true;
        result.ytDlpBrowserName = 'brave';
    } else if (userAgent.includes('OPR') || userAgent.includes('Opera')) {
        result.browser = 'opera';
        result.isSupported = true;
        result.ytDlpBrowserName = 'opera';
    } else if (userAgent.includes('Edg')) {
        result.browser = 'edge';
        result.isSupported = true;
        result.ytDlpBrowserName = 'edge';
    } else if (userAgent.includes('Chrome') && !userAgent.includes('Chromium')) {
        result.browser = 'chrome';
        result.isSupported = true;
        result.ytDlpBrowserName = 'chrome';
    } else if (userAgent.includes('Chromium')) {
        result.browser = 'chromium';
        result.isSupported = true;
        result.ytDlpBrowserName = 'chromium';
    } else if (userAgent.includes('Firefox')) {
        result.browser = 'firefox';
        result.isSupported = true;
        result.ytDlpBrowserName = 'firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        result.browser = 'safari';
        result.isSupported = true;
        result.ytDlpBrowserName = 'safari';
    }

    // 尝试提取版本号
    result.version = extractBrowserVersion(userAgent, result.browser);

    return result;
}

/**
 * 提取浏览器版本号
 * @param userAgent 用户代理字符串
 * @param browserName 浏览器名称
 * @returns 版本号字符串
 */
function extractBrowserVersion(userAgent: string, browserName: string): string | undefined {
    let versionRegex: RegExp;

    switch (browserName) {
        case 'chrome':
            versionRegex = /Chrome\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'chromium':
            versionRegex = /Chromium\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'firefox':
            versionRegex = /Firefox\/(\d+\.\d+)/;
            break;
        case 'safari':
            versionRegex = /Version\/(\d+\.\d+)/;
            break;
        case 'edge':
            versionRegex = /Edg\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'vivaldi':
            versionRegex = /Vivaldi\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'brave':
            // Brave 通常使用 Chrome 版本号
            versionRegex = /Chrome\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'opera':
            versionRegex = /OPR\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        case 'whale':
            versionRegex = /Whale\/(\d+\.\d+\.\d+\.\d+)/;
            break;
        default:
            return undefined;
    }

    const match = userAgent.match(versionRegex);
    return match ? match[1] : undefined;
}

/**
 * 获取当前浏览器的 yt-dlp cookie 参数
 * @returns 如果当前浏览器支持，返回 --cookies-from-browser 参数，否则返回 null
 */
export function getYtDlpCookieParam(): string | null {
    const detection = detectBrowser();
    
    if (detection.isSupported && detection.ytDlpBrowserName) {
        return `--cookies-from-browser ${detection.ytDlpBrowserName}`;
    }
    
    return null;
}

/**
 * 检查指定浏览器是否被 yt-dlp 支持
 * @param browserName 浏览器名称
 * @returns 是否支持
 */
export function isBrowserSupported(browserName: string): boolean {
    return SUPPORTED_BROWSERS.includes(browserName.toLowerCase() as SupportedBrowser);
}

/**
 * 获取所有支持的浏览器选项（用于手动选择）
 * @returns 浏览器选项数组
 */
export function getSupportedBrowserOptions(): Array<{value: SupportedBrowser, label: string}> {
    return SUPPORTED_BROWSERS.map(browser => ({
        value: browser,
        label: browser.charAt(0).toUpperCase() + browser.slice(1)
    }));
}

/**
 * 生成浏览器检测报告（用于调试）
 * @returns 检测报告字符串
 */
export function generateDetectionReport(): string {
    const detection = detectBrowser();
    
    return `
浏览器检测报告:
- 检测到的浏览器: ${detection.browser}
- 版本: ${detection.version || '未知'}
- yt-dlp 支持: ${detection.isSupported ? '是' : '否'}
- yt-dlp 参数名: ${detection.ytDlpBrowserName || '不适用'}
- User Agent: ${detection.userAgent}
    `.trim();
}

/**
 * 浏览器特性检测
 * 检测浏览器是否支持特定的 Web API
 */
export interface BrowserCapabilities {
    /** 是否支持 Chrome 扩展 API */
    supportsExtensions: boolean;
    /** 是否支持 Service Worker */
    supportsServiceWorker: boolean;
    /** 是否支持 Web Components */
    supportsWebComponents: boolean;
    /** 是否支持 ES6 模块 */
    supportsES6Modules: boolean;
}

/**
 * 检测浏览器能力
 * @returns 浏览器能力对象
 */
export function detectBrowserCapabilities(): BrowserCapabilities {
    return {
        supportsExtensions: typeof chrome !== 'undefined' && !!chrome.runtime,
        supportsServiceWorker: 'serviceWorker' in navigator,
        supportsWebComponents: 'customElements' in window,
        supportsES6Modules: 'noModule' in HTMLScriptElement.prototype
    };
}
